{"message_count": {"description": "用于分析群友的聊天记录数量", "type": "int", "hint": "越多越准确，但需要消耗更多时间和Token，到达此值时停止获取聊天记录", "default": 200}, "max_query_rounds": {"description": "查询允许的最大轮数", "type": "int", "hint": "每轮查询200条消息，如果查询的轮数超过最大允许查询的轮数，则停止获取聊天记录，避免查询长时间不发言的人时，执行过多无效查询", "default": 10}, "system_prompt_template": {"description": "系统提示词模板", "type": "text", "hint": "用于指导LLM分析群友的性格，请使用{nickname}和{gender}来表示群友的昵称和性别", "default": "请根据 {nickname} 的聊天记录，分析{gender}的性格特点, 并给出性格标签, 注意要用可爱、调侃的语气，尽量夸奖这位群友，注意给出你的分析过程"}, "llm_provider_id": {"type": "string", "default": "", "description": "LLM提供者ID，用于生成更新总结", "hint": "请填写有效的提供者ID"}}