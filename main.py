from astrbot.api.event import filter
import astrbot.api.message_components as Comp
from astrbot.api.star import Context, Star, register
from astrbot.core.config.astrbot_config import AstrBotConfig
from datetime import datetime, time as dt_time,timedelta
from datetime import  timezone,date

from typing import List, Dict, Any, Optional, Union, Type
import markdown
import textwrap
import math
from astrbot import logger
from astrbot.core.platform.sources.aiocqhttp.aiocqhttp_message_event import (
    AiocqhttpMessageEvent,
)
import base64
import aiohttp
from collections import defaultdict
import json
@register(
    "astrbot_plugin_portrayal",
    "Zhalslar",
    "根据群友的聊天记录，调用llm分析群友的性格画像",
    "1.0.0",
    "https://github.com/Zhalslar/astrbot_plugin_portrayal",
)
class Relationship(Star):
    def __init__(self, context: Context, config: AstrBotConfig):
        super().__init__(context)
        self.config = config
        # 用于分析的消息数量
        self.message_count = config.get("message_count", 200)
        self.llm_provider_id=config.get("llm_provider_id", "")
        # 系统提示词模板
        self.system_prompt_template = config.get(
            "system_prompt_template", "请根据聊天记录分析群友的性格"
        )
        # 最大允许的查询轮数
        self.max_query_rounds = config.get("max_query_rounds", 10)
 
    def _extract_message_text(self, message_segments: List[Dict[str, Any]]) -> str:
            """提取消息文本

            Args:
                message_segments: 消息段列表

            Returns:
                提取的文本内容
            """
            result = ""
            try:
                for segment in message_segments:
                    msg_type = segment.get('type', '')
                    data = segment.get('data', {})

                    if msg_type == 'text':
                        result += data.get('text', '') + ' '
                    elif msg_type == 'face':
                        result += '[表情] '
                    elif msg_type == 'image':
                        # 如果开启了图片文本提取功能，未来可以调用OCR服务
                        result += '[图片] '
                    else:
                        result += f'[{msg_type}] '
            except Exception as e:
                logger.error(f"Error extracting message text: {e}")

            return result
    def profile_to_narrative(self,profile: dict, nickname: str = "") -> str:
        """
        将 profile 字段转换为易于理解的自然语言描述。
        返回一个简短的摘要文本，便于作为 LLM 的上下文输入。
        """
        if not profile:
            return ""
        m = profile.get("message_count", 0)
        c = profile.get("char_count", 0)
        avg = profile.get("avg_text_len", 0.0)
        emoji = profile.get("emoji_count", 0)
        night = profile.get("night_ratio", 0.0)
        hours = profile.get("hours", {})
        first_seen = profile.get("first_seen", "")
        last_seen = profile.get("last_seen", "")
        days = profile.get("days_active", 0)

        # 处理活跃时段（找出前 3 个最活跃的小时段）
        try:
            hours_items = [(int(h), cnt) for h, cnt in hours.items()]
            top3 = sorted(hours_items, key=lambda x: x[1], reverse=True)[:3]
            top_hours = ", ".join([f"{h}时段{cnt}次" for h, cnt in top3])
        except Exception:
            top_hours = ""
        parts = []
        if nickname:
            parts.append(f"{nickname} 的画像摘要：")
        parts.append(f"发言总数{m}条，总字数{c}，平均每条文本长度{avg:.2f}字，表情使用量{emoji}次。")
        parts.append(f"夜间活跃占比{night*100:.1f}%。" if night is not None else "夜间活跃未知。")
        if top_hours:
            parts.append(f"最活跃时段：{top_hours}。")
        if first_seen or last_seen:
            parts.append(f"首次发言时间{first_seen}，最近发言时间{last_seen}。")
        parts.append(f"总活跃天数：{days} 天。")
        return " ".join(parts)
    async def _collect_enhanced_user_features(self, messages: List[Dict[str, Any]]) -> Dict[str, Dict[str, Any]]:
        """
        收集增强的用户特征数据，用于丰富 LLM 的上下文。
        输出结构示例：
        {
        "111111": {
            "qq": 111111,
            "nickname": "昵称",
            "avatar": "data:image/xxx;base64,...",
            "profile": {
            "message_count": 123,
            "char_count": 1024,
            "avg_text_len": 8.3,
            "emoji_count": 25,
            "night_ratio": 0.28,
            "hours": { "0": 2, "1": 0, ..., "23": 5 },
            "first_seen": "2025-01-01T12:00:00",
            "last_seen": "2025-08-31T12:30:00",
            "days_active": 15,
            "top_interactions": [],
            "interacted_with": [],
            "topics_engagement": [],
            "iconography": {"avg_sentence_words": 12},
            "group_role": "Unknown",
            "influence_score": 0.0
            }
        },
        ...
        }
        """
        profiles: Dict[str, Dict[str, Any]] = {}
        if not messages:
            return profiles
        # 将消息按发送者聚合
        user_msgs: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        for msg in messages:
            sender = msg.get('sender', {})
            uid = str(sender.get("user_id", ""))
            if not uid:
                continue
            user_msgs[uid].append(msg)
        for uid, umsgs in user_msgs.items():
            nickname = ""
            first_seen = None
            last_seen = None
            total_chars = 0
            total_text_msgs = 0
            emoji_count = 0
            hours = defaultdict(int)
            total_msgs = len(umsgs)
            for m in umsgs:
                s = m.get("sender", {})
                nickname = s.get("nickname", "") or s.get("card", "")
                t = m.get("time", 0)
                if t:
                    dt = datetime.fromtimestamp(t)
                    if not first_seen or dt < first_seen:
                        first_seen = dt
                    if not last_seen or dt > last_seen:
                        last_seen = dt
                    hours[dt.hour] += 1
                for content in m.get("message", []):
                    if content.get("type") == "text":
                        text = content.get("data", {}).get("text", "")
                        total_chars += len(text)
                        total_text_msgs += 1
                    elif content.get("type") == "face":
                        emoji_count += 1
            avg_text_len = total_chars / total_text_msgs if total_text_msgs > 0 else 0.0
            night_hours = sum(v for k, v in hours.items() if int(k) >= 0 and int(k) <= 6)
            night_ratio = night_hours / total_msgs if total_msgs > 0 else 0.0
            first_seen_iso = first_seen.isoformat() if first_seen else ""
            last_seen_iso = last_seen.isoformat() if last_seen else ""
            days_active = (last_seen.date() - first_seen.date()).days + 1 if first_seen and last_seen else 0
            profile = {
                "message_count": total_msgs,
                "char_count": total_chars,
                "avg_text_len": round(avg_text_len, 2),
                "emoji_count": emoji_count,
                "night_ratio": round(night_ratio, 2),
                "hours": dict(hours),
                "first_seen": first_seen_iso,
                "last_seen": last_seen_iso,
                "days_active": days_active
            }
            enhanced = {
                "qq": int(uid),
                "nickname": nickname,
                "profile": profile
            }
            profiles[uid] = enhanced
        return profiles
    @filter.command("画像")
    async def get_portrayal(self, event: AiocqhttpMessageEvent):
        """
        抽查指定群聊的消息，并分析指定群友画像
        """
        client = event.bot
        group_id = event.get_group_id()
        
        # 获取@的群友
        target_id: str = next(
            (
                str(seg.qq)
                for seg in event.get_messages()
                if (isinstance(seg, Comp.At)) and str(seg.qq) != event.get_self_id()
            ),
            event.get_sender_id(),
        )
        
        logger.info(f"[画像] 开始分析用户: {target_id} 在群 {group_id} 的聊天记录")
        
        nickname, gender = await self.get_nickname(event, target_id)
  
        logger.info(f"[画像] 目标用户信息: 昵称={nickname}, 性别={gender}")
        
        yield event.plain_result(f"稍等，正在为 {nickname} 生成画像...")

        query_rounds = 0
        message_seq = 0
        contexts: list[dict] = []
        total_messages_fetched = 0
        total_messages_filtered = 0
        last_message_seq = None  # 用于检测是否获取到新消息

        all_collected_messages: List[Dict[str, Any]] = []
        # 持续获取群聊历史消息直到达到要求
        while len(contexts) < self.message_count:
            logger.info(f"[画像] 第{query_rounds+1}轮查询, 当前已获取{len(contexts)}条消息")
            
            # 使用修改后的_get_message_history方法
            messages = await self._get_message_history(
                event=event,
                count=200,
                message_seq=message_seq,
                reverse_order=True,
                target_user_id=target_id
            )
            
            total_messages_fetched += len(messages)
            logger.info(f"[画像] 本轮获取到{len(messages)}条原始消息")
            
            if not messages:
                logger.info(f"[画像] 没有更多消息可获取")
                break
                
            # 记录本轮的原始消息，用于增强画像收集
            all_collected_messages.extend(messages)
            
            # 更新message_seq为下一次查询的起始点
            # 当reverse_order=True时，messages[0]是最旧的消息，messages[-1]是最新的消息
            # 下一轮应该从最新消息开始继续向前查询
            message_seq = messages[-1]["message_id"]
            
            
            # 直接处理消息，不再需要单独过滤
            processed_messages = await self._process_messages(
                event=event,
                messages=messages,
                target_user_id=target_id
            )
            
            total_messages_filtered += len(processed_messages)
            logger.info(f"[画像] 本轮处理得到{len(processed_messages)}条有效消息")
            
            contexts.extend([
                {"role": "user", "content": msg}
                for msg in processed_messages
            ])
            
            query_rounds += 1
            if query_rounds >= self.max_query_rounds:
                logger.warning(f"[画像] 达到最大查询轮次限制({self.max_query_rounds})")
                break
        
        logger.info(f"[画像] 查询完成: 共{query_rounds}轮查询, 获取{total_messages_fetched}条原始消息, 过滤得到{total_messages_filtered}条有效消息")
        
        if not contexts:
            logger.error(f"[画像] 没有找到用户{target_id}的聊天记录")
            yield event.plain_result(f"没有找到{nickname}的聊天记录，可能该用户很少发言")
            return
        
        # 结束抓取后，基于累积的原始消息生成增强画像
        enhanced_profiles = await self._collect_enhanced_user_features(all_collected_messages)
        profile = enhanced_profiles.get(target_id, {}).get("profile")
        narrative = self.profile_to_narrative(profile, nickname=nickname)
        user_profile = await self.get_llm_respond(nickname, gender, contexts,narrative)
        if user_profile:
                # 生成图片
                        # 将所有信息整合到user_profile中
                user_profile["nickname"] = nickname
                user_profile["gender"] = gender
                
                # 直接传递整个profile对象
                url = await self._text_to_image(user_profile, target_id, enhanced_profiles)
                yield event.image_result(url)
        else:
                logger.error(f"[画像] 生成分析结果失败")
                yield event.plain_result("分析失败")
     
    async def _get_message_history(
        self, 
        event, 
        count: int, 
        message_seq: int = 0, 
        reverse_order: bool = False,
        target_user_id: str = None
    ) -> List[Dict[str, Any]]:
        """获取消息历史"""
        try:
            group_id = event.get_group_id()
            logger.info(f"[获取消息] 群ID: {group_id}, 获取数量: {count}, 起始ID: {message_seq}, 反序: {reverse_order}, 目标用户: {target_user_id}")

            # 兼容不同cqhttp api
            if hasattr(event.bot, 'get_group_msg_history'):
                response = await event.bot.get_group_msg_history(
                    group_id=group_id,
                    message_seq=message_seq,
                    count=count,
                    reverseOrder=reverse_order
                )
            else:
                response = await event.bot.api.call_action(
                    'get_group_msg_history',
                    group_id=group_id,
                    message_seq=message_seq,
                    count=count,
                    reverseOrder=reverse_order
                )

            messages = response.get('messages', [])
            logger.info(f"[获取消息] API返回{len(messages)}条消息")
            
            # 如果指定了目标用户ID，则过滤消息
            if target_user_id:
                original_count = len(messages)
                messages = [
                    msg for msg in messages 
                    if msg.get('sender', {}).get('user_id') == int(target_user_id)
                ]
                logger.info(f"[获取消息] 过滤后保留{len(messages)}条目标用户的消息 (原{original_count}条)")
            
            return messages
        except Exception as e:
            logger.error(f"[获取消息] 错误: {str(e)}", exc_info=True)
            return []

    async def _process_messages(
        self, 
        event, 
        messages: List[Dict[str, Any]],
        target_user_id: str = None
    ) -> List[str]:
        """处理消息历史记录"""
        chat_records = []
        try:
            logger.info(f"[处理消息] 开始处理{len(messages)}条消息")
            
            for i, msg in enumerate(messages):
                # 获取发送者信息
                sender = msg.get('sender', {})
                sender_id = sender.get('user_id', 'Unknown')
                sender_name = sender.get('nickname', 'Unknown')
                
                # 如果指定了目标用户ID，则检查发送者
                if target_user_id and str(sender_id) != str(target_user_id):
                    logger.debug(f"[处理消息] 跳过非目标用户消息: {sender_name}({sender_id})")
                    continue
                
                # 获取消息时间
                msg_time = msg.get('time', 0)
                time_str = datetime.fromtimestamp(msg_time).strftime('%Y-%m-%d %H:%M:%S')
                
                # 获取消息内容
                message = msg.get('message', [])
                text = self._extract_message_text(message)
                
                if not text.strip():
                    logger.debug(f"[处理消息] 跳过空消息: {sender_name}({sender_id})")
                    continue
                
                # 格式化聊天记录
                record = f"[{time_str}]「{sender_name}」: {text}"
                chat_records.append(record)
                logger.debug(f"[处理消息] 处理消息 {i+1}/{len(messages)}: {record[:50]}...")

            logger.info(f"[处理消息] 处理完成: 共{len(messages)}条消息 -> {len(chat_records)}条有效记录")
            
            # 反转消息顺序（从旧到新）
            chat_records.reverse()
            return chat_records
        except Exception as e:
            logger.error(f"[处理消息] 错误: {str(e)}", exc_info=True)
            return []
    def _extract_json_from_text(self, text: str) -> dict | None:
        """从自由文本中尽量提取 JSON 对象"""
        try:
            json_start = text.find('{')
            json_end = text.rfind('}') + 1
            if json_start != -1 and json_end != -1:
                json_str = text[json_start:json_end]
                import json
                return json.loads(json_str)
        except Exception as e:
            logger.error(f"提取 JSON 失败：{e}")
        return None
    async def get_llm_respond(
        self, nickname: str, gender: str, contexts: list[dict],narrative: str
    ) -> dict | None:
        """调用LLM回复，返回结构化的用户画像数据"""
        try:
            # 构建系统提示词，要求输出JSON格式
            system_prompt = f"""你是一个专业的用户画像分析师，请根据提供的Obsidian知识管理群的聊天记录，分析用户的性格特点和行为模式。这是用户的聊天行为的一些统计数据，供参考：{narrative}
                请以JSON格式返回分析结果，包含以下字段：
                1. title: 给用户的称号（1-5个字）
                2. mbti: 用户的MBTI类型（如ENFP、ISTJ等）
                3. mbti_scores: 包括MBTI各维度得分（0-100）的json结构，表示倾向程度：
                    "E":XX # 外向性
                        "N":XX# 直觉性
                        "F": XX# 情感性
                        "P": XX# 灵活性
                        "creativity": XX# 创造力
                        "social": XX# 社交性
                4. personality_traits: 性格特点列表（3-5个）
                5. communication_style: 沟通风格描述（1-2句话）
                6. interests: 兴趣爱好列表（3-5个）一个英文单词算2个汉字，每个不超过8个汉字
                7. group_role: 在群聊中的角色（1-2句话）
                8. analysis: 详细的性格分析，包括用户的口头禅，经典语录等（200-400字）
                9.age: 心理年龄，通过分析获取用户的心理年龄
                要求：
                - 可选称号建议：
                    - 龙王: 发言频繁但内容轻松的人
                    - 技术专家: 经常讨论技术话题的人
                    - 夜猫子: 经常在深夜发言的人
                    - 表情包军火库: 经常发表情的人
                    - 沉默终结者: 经常开启话题的人
                    - 评论家: 平均发言长度很长的人
                    - 阳角: 在群里很有影响力的人
                    - 互动达人: 经常回复别人的人
                    - 资深玩家：对Obsidian使用非常了解
                    -插件专家：对Obsidian插件有深入了解
                    -整容大师：对Obsidian界面美化css主题等有深入了解
                    -查重率 89% 选手：在科研学术论文方面有深入了解
                    -BUG 亲爹：对编程,插件开发有深入了解
                    - 万金油：对各种话题都有所了解
                    -浑水摸鱼：灌水，刷存在感，偶尔冒泡
                    -潜力大佬/真·扫地僧：虽然偶尔发言，但都是解决问题，具有含金量
                    - ... (可以自行进行拓展添加)
                - 使用{nickname}来称呼用户
                - 使用{"他" if gender == "male" else "她"}作为人称代词
                - 分析要基于聊天记录内容
                - 语言风格要亲切、幽默，带有调侃但不过分
                - MBTI得分要反映实际倾向程度，不是简单的0或100
                - 确保JSON格式正确，不要包含任何解释性文字 
                """
            # 如果有增强画像，追加到系统提示中，作为上下文信息
            provider = self.context.get_provider_by_id(self.llm_provider_id)
            if not provider:
                provider = self.context.get_using_provider()
            # 调用LLM获取响应
            llm_response = await provider.text_chat(
                system_prompt=system_prompt,
                prompt=f"这是{nickname}的聊天记录",
                contexts=contexts,
            )
            # 尝试解析JSON响应
            response_text = getattr(llm_response, "completion_text", "")
            logger.info(f"LLM响应: {response_text}")
            parsed = self._extract_json_from_text(response_text)
            if parsed is not None:
                return parsed
            # 尝试直接解析整个响应文本为 JSON
            import json
            try:
                return json.loads(response_text)
            except json.JSONDecodeError as e:
                logger.error(f"JSON解析失败：{e}, 原始响应: {response_text}")
                return None
        except Exception as e:
            logger.error(f"LLM调用失败：{e}")
            return None

    async def _get_user_avatar(self, user_id: str) -> Optional[str]:
        """获取用户头像的base64编码"""
        try:
            avatar_url = f"https://q4.qlogo.cn/headimg_dl?dst_uin={user_id}&spec=640"
            async with aiohttp.ClientSession() as client:
                response = await client.get(avatar_url)
                response.raise_for_status()
                avatar_data = await response.read()
                # 转换为base64编码
                avatar_base64 = base64.b64encode(avatar_data).decode('utf-8')
                return f"data:image/jpeg;base64,{avatar_base64}"
        except Exception as e:
            logger.error(f"获取用户头像失败 {user_id}: {e}")
            return None
    async def get_nickname(
        self, event: AiocqhttpMessageEvent, user_id: str | int
    ) -> tuple[str, str]:
        """获取指定群友的昵称和性别"""
        client = event.bot
        group_id = event.get_group_id()
        all_info = await client.get_group_member_info(
            group_id=int(group_id), user_id=int(user_id)
        )
        nickname = all_info.get("card") or all_info.get("nickname")
        gender = all_info.get("sex")
        return nickname, gender
    async def _text_to_image(self, user_profile, user_id, enhanced_profiles: Optional[Dict[str, Any]] = None):
        """
        生成用户画像图片
        
        Args:
            user_profile: 包含所有用户信息的字典
            user_id: 用户ID（用于获取头像）
        """
        # 获取用户头像 - 640尺寸的QQ头像
        avatar_data = await self._get_user_avatar(user_id)
            # 处理增强画像 JSON


        enhanced_data = {}
        if enhanced_profiles:
            user_enhanced = enhanced_profiles.get(str(user_id), {})
            if user_enhanced:
                enhanced_data = user_enhanced.get('profile', {})
        logger.info(f"增强画像数据: {enhanced_data}")
        # 计算数据指标
        message_count = enhanced_data.get('message_count', 0)
        avg_text_len = round(enhanced_data.get('avg_text_len', 0), 1)
        days_active = enhanced_data.get('days_active', 0)
        daily_avg_msg = round(message_count / max(days_active, 1), 1)
        night_ratio = enhanced_data.get('night_ratio', 0)
        day_ratio = round((1 - night_ratio) * 100, 1)
        hours_data = enhanced_data.get('hours', {})
        hours_json = json.dumps(enhanced_data.get('hours', {}))
        # 生成洞察标签
        insight_tags = []
        # 固定统计起点
        STATS_START = datetime(2025, 7, 9, tzinfo=timezone.utc)
        first_seen = datetime.fromisoformat(enhanced_data["first_seen"]).astimezone(timezone.utc)
        last_seen  = datetime.fromisoformat(enhanced_data["last_seen"]).astimezone(timezone.utc)
        
        # 实际可观测的最早时间
        observed_start = max(first_seen, STATS_START)
        observed_days = (last_seen.date() - observed_start.date()).days + 1
        # 1. message_count 话痨 / 潜水
        if enhanced_data["message_count"] >= 500:
            insight_tags.append(f"🔥 生产队的驴都没你能唠")
        elif 200 < enhanced_data["message_count"] < 500:
            insight_tags.append(f"💬 就你信息量大")
        elif enhanced_data["message_count"] <= 20:
            insight_tags.append(f"🫧 继续，潜水")


        # 2. avg_text_len 文风
        if enhanced_data["avg_text_len"] < 10:
            insight_tags.append(f"⚡ 惜字如金")
        elif 10 <= enhanced_data["avg_text_len"] <= 20:
            insight_tags.append(f"📝 抠搜的不行")
        elif enhanced_data["avg_text_len"] > 50:
            insight_tags.append(f"📜 就属你能耐")
        else:
            insight_tags.append(f"✍️ 继续划水")

        # 3. night_ratio 昼夜节律
        day_ratio = 100 - enhanced_data["night_ratio"] * 100
        if enhanced_data["night_ratio"] < 0.1:
            insight_tags.append(f"🌞 上班摸鱼")
        elif 0.1 <= enhanced_data["night_ratio"] <= 0.3:
            insight_tags.append(f"🌗 养生打工人")
        else:
            insight_tags.append(f"🦉 守夜冠军")

        # 4. daily_avg_msg 高频/佛系
        # daily_avg = enhanced_data["message_count"] / max(enhanced_data["days_active"], 1)
        # if daily_avg > 15:
        #     insight_tags.append(f"⚡ 消息轰炸，键盘冒火)")
        # elif 5 <= daily_avg <= 15:
        #     insight_tags.append(f"🚀 高频互动，社交牛杂症)")
        # elif daily_avg < 1:
        #     insight_tags.append(f"🧘 佛系划水，摸鱼学大师)")
        # else:
        #     insight_tags.append(f"🧑‍💼 正常节奏，不卷不躺)")

        # 5. days_active 资历
        active_ratio = enhanced_data["days_active"] / max(observed_days, 1)
        if active_ratio >= 0.9:
            insight_tags.append(f"🧠 日更佬")
        elif active_ratio >= 0.75:
            insight_tags.append(f"📚 卷王佬")
        elif active_ratio >= 0.6:
            insight_tags.append(f"📝 高产佬")
        elif active_ratio >= 0.4:
            insight_tags.append(f"🧑‍💼 打卡佬")
        elif active_ratio >= 0.2:
            insight_tags.append(f"🐟 摸鱼佬")
        else:
            insight_tags.append(f"👻 潜水佬")

        # 6. emoji_count
        if enhanced_data["emoji_count"] > enhanced_data["message_count"]:
            insight_tags.append(f"😂 一图走天下)")
        elif enhanced_data["emoji_count"] == 0:
            insight_tags.append("😐 无图言屌")


        # 7. top_hour 活跃高峰
        top_hour = max(enhanced_data["hours"], key=enhanced_data["hours"].get)
        if top_hour in {0, 1, 2}:
            insight_tags.append(f"🌚 凌晨冲浪")
        elif top_hour in {7, 8, 9}:
            insight_tags.append(f"🥱 早八战神")
        elif top_hour == 12 or top_hour == 13:
            insight_tags.append(f"🍱 干饭不耽误")
        elif top_hour in {17, 18}:
            insight_tags.append(f"🛋️ 下班第一喷")
        elif top_hour in {21, 22}:
            insight_tags.append(f"🛌 睡前刷存在")
        else:
            insight_tags.append(f"⏰ 错峰选手")

        # 1. 入群资历（只能按观测区间算）
        
        if observed_days >= 50:
            insight_tags.append(f"📅 全勤打卡")
        elif observed_days >= 30:
            insight_tags.append(f"📆 深度驻扎")
        elif observed_days <= 7:
            insight_tags.append(f"🐣 新号试炼")
        else:
            insight_tags.append(f"🪑 逐渐上头")

        # 生成洞察标签HTML
        insight_tags_html = ""
        for tag in insight_tags:
            insight_tags_html += f'<div class="insight-tag">{tag}</div>'

        # 解析用户资料
        nickname = user_profile.get("nickname", "未知用户")
        title = user_profile.get("title", "神秘群友")
        mbti = user_profile.get("mbti", "ENFP")
        gender = user_profile.get("gender", "")
        age = user_profile.get("age", "")
        reason = user_profile.get("group_role", "该群友在群中表现活跃，是群内的开心果")
        personality_traits = user_profile.get("personality_traits", [])
        communication_style = user_profile.get("communication_style", "善于表达，富有感染力")
        interests = user_profile.get("interests", [])
        analysis = user_profile.get("analysis", "性格开朗，善于与人交往，富有创造力和想象力")
        scores = user_profile.get('mbti_scores', {})
        
        # 处理性格特点标签，确保有6个
        if not personality_traits:
            personality_traits = ["幽默风趣", "善于沟通", "创意满满", "乐观开朗", "有趣灵魂", "活跃分子"]
        
        personality_traits = personality_traits[:6]
        default_traits = ["幽默风趣", "善于沟通", "创意满满", "乐观开朗", "有趣灵魂", "活跃分子"]
        while len(personality_traits) < 6:
            for trait in default_traits:
                if trait not in personality_traits:
                    personality_traits.append(trait)
                    break
            if len(personality_traits) >= 6:
                break
        
        # 生成MBTI雷达图数据
        if scores:
            radar_data = [
                scores.get('E', 75),
                scores.get('N', 80),
                scores.get('F', 85),
                scores.get('P', 90),
                scores.get('creativity', 88),
                scores.get('social', 82)
            ]
        else:
            radar_data = [85, 90, 88, 92, 95, 87]

        # 将雷达图数据转为字符串，避免JSON解析
        radar_data_str = ','.join(map(str, radar_data))
        
        tmpl = """<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700;900&display=swap');
        
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: 'Noto Sans SC', 'PingFang SC', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #E3F2FD 0%, #BBDEFB 100%);
            width: 1080px;
      
            overflow-x: hidden;
            font-size: 40px;
        }

        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            line-height: 1.3;
            margin-top: 1.4em;
            margin-bottom: 0.7em;
            color: #2C3E50;
        }
        h2 {
            font-size: 1.5em;
            margin-top: 1em;
            margin-bottom: 0.5em;
        }
        h3 {
            font-size: 1.25em;
        }
        
        .container {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 100px 60px;
            padding-bottom: 0px;
        }

        .user-avatar-section {
            position: relative;
            margin-bottom: -300px;
            margin-top:50px;
        }

        .user-avatar {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            border: 12px solid white;
            box-shadow: 0 16px 50px rgba(0, 0, 0, 0.2);
            object-fit: cover;
            position: relative;
            z-index: 10;
        }

        .user-avatar-placeholder {
            width: 300px;
            height: 300px;
            border-radius: 50%;
            border: 12px solid white;
            background: #F5F5F5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 140px;
            color: #999;
            box-shadow: 0 16px 50px rgba(0, 0, 0, 0.2);
            position: relative;
            z-index: 10;
        }

        .floating-tags {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 600px;
            height: 500px;
            pointer-events: none;
            z-index: 6;
        }

        .tag {
            position: absolute;
            background: #2196F3;
            color: white;
            padding: 18px 36px;
            border-radius: 40px;
            font-size: 28px;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(33, 150, 243, 0.4);
            white-space: nowrap;
            animation: float 4s ease-in-out infinite;
            cursor: default;
            opacity: 0.8;
        }

        .tag:nth-child(1) {
            top: 40px;
            left: 20px;
            transform: translateX(-20%);
            background: #FF6B6B;
            animation-delay: 0s;
        }

        .tag:nth-child(2) {
            top: 30px;
            right: -15px;
            background: #4ECDC4;
            animation-delay: 0.8s;
        }

        .tag:nth-child(3) {
            top: 270px;
            left: 5px;
            transform: translateX(-20%);
            background: #45B7D1;
            animation-delay: 1.6s;
        }

        .tag:nth-child(4) {
            top: 160px;
            left: 10px;
            transform: translateX(-40%);
            background: #96CEB4;
            animation-delay: 2.4s;
        }

        .tag:nth-child(5) {
            top: 140px;
            right: -20px;
            background: #FFEAA7;
            color: #2d3436;
            animation-delay: 3.2s;
        }

        .tag:nth-child(6) {
            top: 240px;
            right: -50px;
            background: #DDA0DD;
            animation-delay: 4s;
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(var(--x-offset, 0));
            }
            50% {
                transform: translateY(-16px) translateX(var(--x-offset, 0));
            }
        }

        .tag:nth-child(1) { --x-offset: -50%; }
        .tag:nth-child(3) { --x-offset: -50%; }

        .user-info-card {
            background: white;
            border-radius: 35px;
            padding: 70px 80px;
            box-shadow: 0 16px 50px rgba(0, 0, 0, 0.15);
            text-align: center;
            max-width: 950px;
            width: 100%;
            margin-top: 150px;
            margin-bottom: 70px;
            position: relative;
            z-index: 5;
        }

        .user-name {
            font-size: 64px;
            font-weight: 700;
            color: #2C3E50;
            margin-bottom: 25px;
            margin-top: 120px;
        }

        .user-basic-info {
            font-size: 36px;
            color: #7F8C8D;
            margin-bottom: 45px;
        }

        .badges-section {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin: 40px 0;
            flex-wrap: wrap;
        }

        .badge {
            background: #2196F3;
            color: white;
            padding: 18px 36px;
            border-radius: 40px;
            font-size: 28px;
            font-weight: 500;
            box-shadow: 0 5px 20px rgba(33, 150, 243, 0.4);
        }

        .mbti-badge {
            background: #FF6B6B;
            box-shadow: 0 5px 20px rgba(255, 107, 107, 0.4);
        }

        /* 新增：数据面板样式 */
        .data-dashboard {
             background: linear-gradient(135deg, #edf3f6 0%, #fffdfd 100%);
            border-radius: 25px;
            padding: 50px;
            margin: 50px 0;

            text-align: center;
        }

        .dashboard-title {
              font-size: 1.25em;
            font-weight: 700;
            margin-bottom: 40px;
          
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .stat-card {
            background:rgb(33 150 243 / 78%);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .stat-number {
            font-size: 48px;
            font-weight: 900;
            margin-bottom: 10px;
            color: #FFE082;
        }

        .stat-label {
            font-size: 24px;
            opacity: 0.9;
                color: white;
        }

        .activity-chart {
            background: rgb(207 231 251 / 53%);
            border-radius: 20px;
            padding: 30px 30px 58px 30px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
        }

        .activity-title {
              font-size: 1.25em;
            font-weight: 600;
            margin-bottom: 25px;
      
        }

        .time-bars {
            display: grid;
            grid-template-columns: repeat(12, 1fr);
            gap: 8px;
            height: 120px;
            align-items: end;
        }

        .time-bar {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            position: relative;
            transition: all 0.3s ease;
            min-height: 20px;
        }

        .time-bar.active {
            background: linear-gradient(to top, #FFE082, #FFF59D);
            box-shadow: 0 0 20px rgba(255, 224, 130, 0.5);
        }

        .time-label {
            position: absolute;
            bottom: -35px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 18px;
            opacity: 0.8;
            white-space: nowrap;
        }

        .info-sections {
            display: grid;
            grid-template-columns: 1fr;
            gap: 40px;
            margin-top: 50px;
            text-align: left;
        }
        @media (min-width: 768px) {
            .info-sections {
                grid-template-columns: 1fr 1fr;
            }
        }

        .info-section {
            background: #F8FAFB;
            padding: 40px;
            border-radius: 20px;
            border-left: 8px solid #2196F3;
        }

        .info-title {
            font-size: 36px;
            font-weight: 600;
            color: #2196F3;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            line-height: 1.2;
        }

        .info-title::before {
            content: '●';
            margin-right: 15px;
            font-size: 28px;
        }

        .info-content {
            font-size: 32px;
            color: #34495E;
            line-height: 1.8;
            text-align: left;
        }

        .content {
            font-size: 40px;
            color: #34495E;
            line-height: 1.8;
        }

        .radar-section {
            margin-top: 60px;
            padding-top: 50px;
            border-top: 4px solid #E8F4FD;
            text-align: center;
        }

        .radar-title {
            font-size: 40px;
            font-weight: 600;
            color: #2C3E50;
            margin-bottom: 40px;
            line-height: 1.2;
        }

        .footer {
            width: 100%;
            text-align: center;
            color: #7F8C8D;
            font-size: 24px;
            margin-top: 40px;
            padding: 40px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .bg-decoration {
            position: absolute;
            width: 200px;
            height: 200px;
            border-radius: 50%;
            opacity: 0.1;
            z-index: 1;
        }

        .bg-decoration.circle1 {
            top: -100px;
            right: -100px;
            background: #2196F3;
        }

        .bg-decoration.circle2 {
            bottom: -100px;
            left: -100px;
            background: #FF6B6B;
        }

        .bg-decoration.circle3 {
            top: 50%;
            right: -50px;
            background: #4ECDC4;
            width: 100px;
            height: 100px;
        }

        .interests-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
            margin-top: 20px;
        }

        .interest-tag {
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 28px;
            font-weight: bold;
            white-space: nowrap;
            transition: all 0.3s ease;
            cursor: default;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .interest-tag:nth-child(1) {
            background: #FFEBEE;
            color: #C62828;
            border: 1px solid #EF9A9A;
        }
        .interest-tag:nth-child(1):hover {
            background: #EF9A9A;
            color: #FFF;
            box-shadow: 0 4px 8px rgba(239, 154, 154, 0.4);
        }

        .interest-tag:nth-child(2) {
            background: #E8F5E9;
            color: #2E7D32;
            border: 1px solid #A5D6A7;
        }
        .interest-tag:nth-child(2):hover {
            background: #A5D6A7;
            color: #FFF;
            box-shadow: 0 4px 8px rgba(165, 214, 167, 0.4);
        }

        .interest-tag:nth-child(3) {
            background: #F3E5F5;
            color: #6A1B9A;
            border: 1px solid #CE93D8;
        }
        .interest-tag:nth-child(3):hover {
            background: #CE93D8;
            color: #FFF;
            box-shadow: 0 4px 8px rgba(206, 147, 216, 0.4);
        }

        .interest-tag:nth-child(4) {
            background: #E3F2FD;
            color: #1976D2;
            border: 1px solid #BBDEFB;
        }
        .interest-tag:nth-child(4):hover {
            background: #BBDEFB;
            color: #FFF;
            box-shadow: 0 4px 8px rgba(187, 222, 251, 0.4);
        }

        .interest-tag:nth-child(5) {
            background: #FFFDE7;
            color: #FBC02D;
            border: 1px solid #FFEE58;
        }
        .interest-tag:nth-child(5):hover {
            background: #FFEE58;
            color: #FFF;
            box-shadow: 0 4px 8px rgba(255, 238, 88, 0.4);
        }

        .interest-tag:nth-child(6) {
            background: #E0F7FA;
            color: #00838F;
            border: 1px solid #80DEEA;
        }
        .interest-tag:nth-child(6):hover {
            background: #80DEEA;
            color: #FFF;
            box-shadow: 0 4px 8px rgba(128, 222, 234, 0.4);
        }

        /* 新增：数据洞察标签样式 */
        .insight-tag {
            background: linear-gradient(45deg, #4fabf4, #acd8fa);
            text-shadow: 1px 1px 0 #408ac6;
            color: white;
            padding: 15px 30px;
            border-radius: 30px;
            font-size: 22px;
            font-weight: 600;
            margin: 10px;
            display: inline-block;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }

        .insights-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            margin-top: 30px;
            backdrop-filter: blur(10px);
        }

        .insights-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
    </style>
</head>
<body>
    <div class="bg-decoration circle1"></div>
    <div class="bg-decoration circle2"></div>
    <div class="bg-decoration circle3"></div>
    
    <div class="container">
        <!-- 用户头像和标签区域 -->
        <div class="user-avatar-section">
            <div class="floating-tags">
                <div class="tag">{{ trait1 }}</div>
                <div class="tag">{{ trait2 }}</div>
                <div class="tag">{{ trait3 }}</div>
                <div class="tag">{{ trait4 }}</div>
                <div class="tag">{{ trait5 }}</div>
                <div class="tag">{{ trait6 }}</div>
            </div>
            {{ avatar_html }}
        </div>

        <!-- 用户信息卡片 -->
        <div class="user-info-card">
            <div class="user-name">{{ nickname }}</div>
            <div class="user-basic-info">
                心理年龄{{ age }}岁，{{ gender_text }}，{{ mbti }} 人格类型
            </div>
            
            <div class="badges-section">
                <div class="badge">{{ title }}</div>
                <div class="badge mbti-badge">{{ mbti }}</div>
            </div>
      <div class="info-section">
                <div class="info-content">{{ reason }}</div>
            </div>
            <!-- 数据面板 -->
            <div class="data-dashboard">
                <div class="dashboard-title">📊 用户数据洞察</div>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-number">{{ message_count }}</div>
                        <div class="stat-label">总消息数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ avg_text_len }}</div>
                        <div class="stat-label">平均字数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number">{{ daily_avg_msg }}</div>
                        <div class="stat-label">日均消息</div>
                    </div>
                </div>

                <div class="activity-chart">
                    <div class="activity-title">⏰ 24小时活跃度分布</div>
                    <div class="time-bars" id="timeBars"></div>
                </div>

                <div class="insights-section">
                    <div class="activity-title">🔍 行为洞察</div>
                    <div class="insights-grid">
                        {{ insight_tags_html }}
                    </div>
                </div>
            </div>

      

            <div class="info-sections">
                <div>
                    <div class="info-title">沟通风格</div>
                    <div class="info-content">{{ communication_style }}</div>
                </div>
                
                <div>
                    <div class="info-title">兴趣爱好</div>
                    <div class="info-content">
                        <div class="interests-list">
                            {{ interests_html }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="radar-section">
                <h3>🛍️详细分析</h3>
                <div class="content">{{ analysis }}</div>
            </div>

            <!-- MBTI雷达图区域 -->
            <div class="radar-section">
                <h3>🎯 性格特征雷达图</h3>
                <canvas id="radarChart" width="500" height="400"></canvas>
            </div>
        </div>

        <div class="footer">
            由 PKMer AI 助手生成 • 仅供参考 • {{ date_str }}
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
    <script>
        // 生成活跃时间条形图
        function generateActivityBars() {
            // 使用模板变量传入的小时数据
           const hours = {{ hours_data  | safe }};
            
            const container = document.getElementById('timeBars');
            const maxValue = Math.max(...Object.values(hours)) || 1;
            
            // 选择主要活跃时间段显示（避免过于拥挤）
            const displayHours = ['7', '9', '11', '13', '15', '17', '19', '21', '23', '1', '3', '5'];
            
            displayHours.forEach(hour => {
                const value = hours[hour] || 0;
                const height = Math.max(20, (value / maxValue) * 100);
                
                const bar = document.createElement('div');
                bar.className = 'time-bar';
                if (value > maxValue * 0.3) {
                    bar.classList.add('active');
                }
                bar.style.height = height + 'px';
                
                const label = document.createElement('div');
                label.className = 'time-label';
                label.textContent = hour + '时';
                bar.appendChild(label);
                
                container.appendChild(bar);
            });
        }

        // 生成MBTI雷达图
        function generateRadarChart() {
            const ctx = document.getElementById('radarChart').getContext('2d');
            
            const mbtiData = {
                labels: ['外向性', '直觉性', '情感性', '灵活性', '创造力', '社交性'],
                datasets: [{
                    label: 'ENFP 特征',
                    data: [85, 90, 88, 92, 95, 87],
                    backgroundColor: 'rgba(33, 150, 243, 0.2)',
                    borderColor: '#2196F3',
                    borderWidth: 4,
                    pointBackgroundColor: '#FF6B6B',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 4,
                    pointRadius: 10
                }]
            };

            new Chart(ctx, {
                type: 'radar',
                data: mbtiData,
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                font: {
                                    size: 20
                                },
                                color: '#666',
                                stepSize: 20
                            },
                            grid: {
                                color: 'rgba(33, 150, 243, 0.2)'
                            },
                            angleLines: {
                                color: 'rgba(33, 150, 243, 0.3)'
                            },
                            pointLabels: {
                                font: {
                                    size: 28,
                                    weight: '500'
                                },
                                color: '#2C3E50'
                            }
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    elements: {
                        line: {
                            tension: 0.3
                        }
                    }
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            generateActivityBars();
            generateRadarChart();
        });
    </script>
</body>
</html>"""

        # 处理头像HTML
        if avatar_data:
            avatar_html = f'<img src="{avatar_data}" class="user-avatar" alt="头像">'
        else:
            avatar_html = '<div class="user-avatar-placeholder">👤</div>'
        
        # 处理兴趣爱好HTML
        interests_html = ""
        if interests:
            for interest in interests:
                display_interest = self.truncate_interest(interest, max_display_len=8)
                interests_html += f'<div class="interest-tag">{display_interest}</div>'
        else:
            interests_html = '<div>多元化兴趣，乐于尝试新事物</div>'
         
        # 处理性别显示
        gender_text = ""
        if gender == "male":
            gender_text = "男性"
        elif gender == "female":
            gender_text = "女性"
        else:
            gender_text = ""

        render_options = {
            "type": "jpeg", 
            "quality": 90,
            "full_page": True,
            "clip": {
                "x": 0,
                "y": 0,
                "width": 1080,
                "height": 99999
            },
            "scale": "device",
            "animations": "disabled",
            "caret": "hide"
        }
        
        date_str = datetime.now().strftime("%Y-%m-%d")
        
        render_data = {
            "avatar_html": avatar_html,
            "nickname": nickname,
            "title": title,
            "mbti": mbti,
            "gender_text": gender_text,
            "age": age or "",
            "reason": reason,
            "trait1": personality_traits[0],
            "trait2": personality_traits[1],
            "trait3": personality_traits[2],
            "trait4": personality_traits[3],
            "trait5": personality_traits[4],
            "trait6": personality_traits[5],
            "communication_style": communication_style,
            "interests_html": interests_html,
            "analysis": analysis,
            "radar_data_str": radar_data_str,
            "date_str": date_str,
            "message_count": message_count,
            "avg_text_len": avg_text_len,
            "days_active": days_active,
            "daily_avg_msg": daily_avg_msg,
            "hours_data": hours_json,
            "insight_tags_html": insight_tags_html,
        }
        
        url = await self.html_render(tmpl, render_data, return_url=True, options=render_options)
        return url

    def truncate_interest(self,interest, max_display_len=8):
        if 'Obsidian' not in interest:
            # 普通情况：按字符长度判断
            if len(interest) <= max_display_len:
                return interest
            else:
                return interest[:max_display_len] + '...'
        else:
            # 包含 Obsidian 的特殊情况，Obsidian 计为 3 个字符长度
            res = ''
            disp = 0
            i = 0
            while i < len(interest) and disp < max_display_len:
                if interest.startswith('Obsidian', i):
                    if disp + 3 > max_display_len:
                        break
                    res += 'Obsidian'
                    i += len('Obsidian')
                    disp += 3
                else:
                    ch = interest[i]
                    if disp + 1 > max_display_len:
                        break
                    res += ch
                    i += 1
                    disp += 1
            # 未完全截断时，补上省略号
            if i < len(interest):
                res += '...'
            return res
    # 对应的调用方式修改：
     

    # user_profile 预期的数据结构：
    """
    user_profile = {
        "nickname": "悦己享乐者",
        "title": "神秘群友", 
        "mbti": "ENFP",
        "gender": "female",  # "male" 或 "female"
        "age": 26,
        "personality_traits": ["幽默风趣", "善于沟通", "创意满满", "乐观开朗"],
        "communication_style": "善于表达，富有感染力，喜欢用表情包",
        "interests": ["科技数码", "时尚潮流", "网购", "社交媒体"],
        "group_role": "该群友在群中表现活跃，是群内的开心果",
        "analysis": "性格开朗外向，富有创造力，善于与人交往...",
        "mbti_scores": {
            "E": 85,  # 外向性
            "N": 90,  # 直觉性
            "F": 88,  # 情感性
            "P": 92,  # 灵活性
            "creativity": 95,  # 创造力
            "social": 87  # 社交性
        }
    }
    """